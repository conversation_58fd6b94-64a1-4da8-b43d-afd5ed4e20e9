package io.iotex.iopay.support.eventbus

import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.Wallet
import java.math.BigDecimal

class AddERC20Event

class NetworkSwitchEvent(val from: RPCNetwork, val to: RPCNetwork)

class SwitchWalletEvent

class MainPullRefresh

class MainCardEyeRefresh

class RefreshBalanceFinishEvent

class TokenLoadFinishEvent

class FavoriteOrDislikeERC20Event

class ActionRefreshEvent

class WalletAvatarChangeEvent

class RefreshNetworkListEvent

class RefreshRpcNodeListEvent

class MetaUploadErrorEvent

class MetaUploadLocationErrorEvent(val weakGps: Boolean)

class MetaPermissionErrorEvent

class RenameWalletEvent(val wallet: Wallet)

class RefreshDAppEvent

class BoundEmailRefreshEvent

class MainPageEvent(val page:Int)

class WebViewDePinErrorEvent

class WebViewDePinRefreshErrorEvent

class WebViewBinoAiRefreshErrorEvent

class RefreshAAWalletStatusEvent

class MessageRedDotEvent

class NewsRedDotEvent

class DAppChainEvent(val name: String)

class SwitchAllNetworkEvent

class AllBalanceEvent(val balance: BigDecimal)

class DAppPageEvent

class BinoAiPageEvent

class GiftPageEvent(val isVisitor:Boolean)

class GiftUploadEvent(val taskId:Int)

class RefreshMainPriceEvent
