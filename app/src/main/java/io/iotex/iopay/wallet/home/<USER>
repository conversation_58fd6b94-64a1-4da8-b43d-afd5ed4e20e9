package io.iotex.iopay.wallet.home

import android.content.Intent
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.ellipsis
import com.robinhood.ticker.TickerUtils
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.FragmentWalletCardBinding
import io.iotex.iopay.meta.SP_KEY_FIRST_SWITCH_TO_BITCOIN
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.record.ActionRecordActivity
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.AllBalanceEvent
import io.iotex.iopay.support.eventbus.BoundEmailRefreshEvent
import io.iotex.iopay.support.eventbus.MainCardEyeRefresh
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshAAWalletStatusEvent
import io.iotex.iopay.support.eventbus.RefreshBalanceFinishEvent
import io.iotex.iopay.support.eventbus.RenameWalletEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.WalletAvatarChangeEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.NetworkUtils
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.LearnMoreActivity
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.aawallet.dialog.ActiveWalletDialog
import io.iotex.iopay.wallet.aawallet.dialog.RecoveryWalletDialog
import io.iotex.iopay.wallet.aawallet.dialog.StopRecoveryDialog
import io.iotex.iopay.wallet.aawallet.dialog.WalletExpiredDialog
import io.iotex.iopay.wallet.bitcoin.BitcoinAddressDialog
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.receive.ReceiveActivity
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity
import io.iotex.iopay.wallet.viewmodel.AAWalletStatus
import io.iotex.iopay.wallet.viewmodel.AAWalletStatusWrapper
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.web3.Web3Delegate
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread

class WalletCardFragment :
    BaseBindFragment<WalletCardViewModel, FragmentWalletCardBinding>(R.layout.fragment_wallet_card) {

    private val aaWalletViewModel by lazy {
        ViewModelProvider(this)[AAWalletViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private var promptDialog: DialogFragment? = null
    private var allBalance: String = "--"

    override fun useEventBus(): Boolean {
        return true
    }

    override fun initView() {
        mBinding.llCopy.setOnClickListener {
            if (UserStore.getAllNetwork()) {
                ReceiveActivity.startActivity(requireContext(), title = Utils.getApp().getString(R.string.wallet_address_only))
                return@setOnClickListener
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_CLICK_COPY_ADDRESS)
            val address =
                Constant.currentWallet?.getCurNetworkAddress() ?: UserStore.getWalletAddress()
            val addressFormat = WalletHelper.formatWalletAddress(address)
            ClipboardUtils.copyText(addressFormat)
            if (Constant.currentWallet?.isAAWallet() == true) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }
        mBinding.ivBalancesEye.setOnClickListener {
            val isHide = SPUtils.getInstance().getBoolean(SPConstant.SP_BALANCES_IS_HIDE, false)
            SPUtils.getInstance().put(SPConstant.SP_BALANCES_IS_HIDE, !isHide)
            mBinding.ivBalancesEye.isSelected = !isHide
            showAllBalance()
            EventBus.getDefault().post(MainCardEyeRefresh())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_CLICK_VISIBLE_BUTTON)
        }

        mBinding.llSend.setOnClickListener {
            if (!isWatchWallet()) {
                if (WalletHelper.checkWalletExpired()) return@setOnClickListener
                TransferActivity.start(requireContext())
                FireBaseUtil.logFireBase(FireBaseEvent.SEND_BTN)
                PageEventUtil.logEvent(PageEventUtil.SEND)
            } else {
                ToastUtils.showShort(R.string.watch_wallet_warning)
            }
            if (UserStore.getAllNetwork()) {
                mViewModel.switchNetworkOnAllNet(Config.IOTEX_CHAIN_ID)
            }
        }
        mBinding.llReceive.setOnClickListener {
            if (!UserStore.getAllNetwork() || Constant.currentWallet?.isBitcoinWatchWallet() == true) {
                val addressType = Constant.currentWallet?.addressType ?: AddressType_Legacy
                ReceiveDetailActivity.startActivity(
                    requireContext(),
                    UserStore.getChainId(),
                    addressType
                )
            } else {
                ReceiveActivity.startActivity(requireContext())
            }
            FireBaseUtil.logFireBase(FireBaseEvent.RECEIVE_BTN)
            PageEventUtil.logEvent(PageEventUtil.RECEIVE)
        }

        mBinding.llBuy.setOnClickListener {
            val intent = Intent(activity, ReactNativeActivity::class.java)
            intent.putExtra(ReactNativeActivity.REACT_COMPONENT_NAME, ReactScene.BuyIOTX.name)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_MENU_BUY)
        }

        mBinding.llActivity.setOnClickListener {
            startActivity(Intent(activity, ActionRecordActivity::class.java))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_ACTIVITIES)
        }

        mBinding.llEarn.setOnClickListener {
            val intent = Intent(activity, ReactNativeActivity::class.java)
            intent.putExtra(ReactNativeActivity.REACT_COMPONENT_NAME, ReactScene.EarningList.name)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_EARN)
        }

        mBinding.ivWalletAlert.setGone()
        mBinding.ivWalletAlert.setOnClickListener {
            aaWalletViewModel.aaWalletStatusLD.value?.let {
                resolveAAWalletStatus(it)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_WALLET_RED_REMINDER)
        }

        mBinding.ivBitcoinAddressChange.setOnClickListener {
            BitcoinAddressDialog(
                mViewModel.walletLiveData.value?.addressType ?: AddressType_Legacy
            ).apply {
                onCheckAddress = {
                    mBinding.tvAddress.text = it.bitcoinAddress.ellipsis(6,6)
                    EventBus.getDefault().post(SwitchWalletEvent())
                }
            }.show(childFragmentManager, System.currentTimeMillis().toString())
        }
    }

    override fun initData() {
        mViewModel.curNetWork()
        mViewModel.curWallet()
        aaWalletViewModel.queryAAWalletStatus()
        checkAllNetwork()
    }

    override fun initEvent() {
        mViewModel.networkLiveData.observe(this) { network ->
            if (UserStore.getAllNetwork()) return@observe
            if (network.chainId != Config.IOTEX_CHAIN_ID) {
                SPUtils.getInstance().put(SPConstant.TRANSFER_TIPS_DIALOG, false)
            }

            showCardButton(network.chainId)

            if (!WalletHelper.isBitcoinNetwork()) {
                NetworkUtils.checkNetwork(network.rpc)
            }
        }

        mViewModel.walletLiveData.observe(this) { wallet ->
            mBinding.ivBitcoinAddressChange.isVisible =
                WalletHelper.isBitcoinNetwork() && !UserStore.getAllNetwork()
                        && wallet.isBitcoinWatchWallet() == false
            showWallet(wallet)
            mViewModel.isValidAccount()
        }

        mViewModel.isValidAccountLiveData.observe(this) {
            if (!it) {
                mBinding.llWalletTips.visibility = View.VISIBLE
                mBinding.llWalletTips.setOnClickListener {
                    startActivity(Intent(context, LearnMoreActivity::class.java))
                }
            } else {
                mBinding.llWalletTips.visibility = View.GONE
            }
        }

        aaWalletViewModel.aaWalletStatusLD.observe(this) {
            if (mViewModel.walletLiveData.value?.isAAWallet() == true) {
                resolveAAWalletStatus(it)
            } else {
                mBinding.ivWalletAlert.setGone()
            }
        }
    }

    private fun resolveAAWalletStatus(statusWrapper: AAWalletStatusWrapper) {
        promptDialog?.dismissAllowingStateLoss()
        when (statusWrapper.status) {
            AAWalletStatus.SELF_RECOVERING -> {
                mBinding.ivWalletAlert.setVisible()
                promptDialog = RecoveryWalletDialog(statusWrapper.recoveryTimestamp)
                promptDialog?.show(
                    requireActivity().supportFragmentManager,
                    "recovery_wallet_dialog"
                )
            }

            AAWalletStatus.OTHER_RECOVERING -> {
                mBinding.ivWalletAlert.setGone()
                promptDialog = StopRecoveryDialog()
                promptDialog?.show(requireActivity().supportFragmentManager, "stop_recovery_dialog")
            }

            AAWalletStatus.AVAILABLE -> {
                mBinding.ivWalletAlert.setGone()
            }

            AAWalletStatus.UNAVAILABLE -> {
                mBinding.ivWalletAlert.setVisible()
                promptDialog = WalletExpiredDialog()
                promptDialog?.show(
                    requireActivity().supportFragmentManager,
                    "wallet_expired_dialog"
                )
            }

            AAWalletStatus.UNBOUND -> {
                mBinding.ivWalletAlert.setVisible()
                promptDialog = ActiveWalletDialog()
                promptDialog?.show(requireActivity().supportFragmentManager, "active_wallet_dialog")
            }
        }
    }

    private fun showWallet(wallet: Wallet) {
        if (WalletHelper.isBitcoinNetwork() || WalletHelper.isSolanaNetwork()) {
            mBinding.tvAddress.text = wallet.getCurNetworkAddress().ellipsis(6,6)
        } else {
            mBinding.tvAddress.text = getAddress(wallet.address)
        }
        val isHide = SPUtils.getInstance().getBoolean(SPConstant.SP_BALANCES_IS_HIDE, false)
        mBinding.ivBalancesEye.isSelected = isHide
        EventBus.getDefault().post(RefreshBalanceFinishEvent())
    }

    private fun showCardButton(chainId: Int) {
        if (UserStore.getAllNetwork()) {
            mBinding.llSend.setVisible()
            mBinding.llReceive.setVisible()
            if (Constant.currentWallet?.isSolanaPrivateWallet() == true
                || Constant.currentWallet?.isSolanaWatchWallet() == true
                || Constant.currentWallet?.isBitcoinWatchWallet() == true
            ) {
                mBinding.llBuy.setGone()
                mBinding.llEarn.setGone()
            } else {
                mBinding.llBuy.setVisible()
                mBinding.llEarn.setVisible()
            }
            return
        }
        when (chainId) {
            Config.IOTEX_CHAIN_ID -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setVisible()
                mBinding.llEarn.setVisible()
            }

            Config.IOTEX_NIGHT_CHAIN_ID -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setVisible()
                mBinding.llEarn.setVisible()
            }

            Config.IOTEX_TEST_CHAIN_ID -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setVisible()
                mBinding.llEarn.setVisible()
            }

            Config.ETH_CHAIN_ID, Config.BSC_CHAIN_ID, Config.POLYGON_CHAIN_ID -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setGone()
                mBinding.llEarn.setGone()
            }

            Config.BITCOIN_TEST_CHAIN_ID -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setGone()
                mBinding.llEarn.setGone()
            }

            else -> {
                mBinding.llSend.setVisible()
                mBinding.llReceive.setVisible()
                mBinding.llBuy.setGone()
                mBinding.llEarn.setGone()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        checkAllNetwork()
        mViewModel.curNetWork()
        mViewModel.curWallet()
        checkFirstBitCoin()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        mBinding.ivWalletAlert.setGone()
        mViewModel.curWallet()
        aaWalletViewModel.queryAAWalletStatus()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: ActionRefreshEvent) {
        mViewModel.curWallet()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletAvatarChangeEvent(event: WalletAvatarChangeEvent) {
        mViewModel.curWallet()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRRenameWalletEvent(event: RenameWalletEvent) {
        mViewModel.curWallet()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainPullRefresh(event: MainPullRefresh) {
        mViewModel.curNetWork()
        mViewModel.curWallet()
        aaWalletViewModel.queryAAWalletStatus()
        aaWalletViewModel.checkService()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBoundEmailRefreshEvent(event: BoundEmailRefreshEvent) {
        mBinding.ivWalletAlert.setGone()
        mViewModel.curWallet()
        aaWalletViewModel.queryAAWalletStatus()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBoundEmailRefreshEvent(event: RefreshAAWalletStatusEvent) {
        aaWalletViewModel.queryAAWalletStatus()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchAllNetworkEvent(event: SwitchAllNetworkEvent) {
        checkAllNetwork()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onAllBalanceEvent(event: AllBalanceEvent) {
        allBalance = "$${TokenUtil.displayPrice(event.balance.toString())}"
        showAllBalance()
        checkAllNetwork()
    }

    private fun showAllBalance(){
        mBinding.tvWalletValue.setCharacterLists(TickerUtils.provideNumberList())
        val hide = SPUtils.getInstance().getBoolean(SPConstant.SP_BALANCES_IS_HIDE)
        mBinding.tvWalletHide.isVisible = hide
        mBinding.tvWalletValue.isVisible = !hide
        mBinding.shimmer.shimmerLayout.isVisible = allBalance.isEmpty()
        mBinding.tvWalletValue.text = allBalance
    }

    private fun checkAllNetwork() {
        mBinding.llCopy.isVisible = !UserStore.getAllNetwork()
        if (UserStore.getAllNetwork()) showCardButton(Config.IOTEX_CHAIN_ID)
    }

    private fun isWatchWallet(): Boolean {
        return mViewModel.walletLiveData.value?.isWatch == true
    }

    private fun getAddress(address: String): String {
        if (!WalletHelper.isIoTexNetWork()) {
            return WalletHelper.formatWalletAddress(address).ellipsis(6,6)
        }
        val web3Address = WalletHelper.convertWeb3Address(address)
        val localName = SPUtils.getInstance().getString(SPConstant.SP_INS_NAME_OF + web3Address, "")
        doAsync {
            val name = Web3Delegate.addressToIns(address)
            uiThread {
                if (localName != name && name.isNotEmpty()) {
                    SPUtils.getInstance().put(SPConstant.SP_INS_NAME_OF + web3Address, name)
                    mBinding.tvAddress.text = name
                }
            }
        }
        return localName.ifEmpty {
            WalletHelper.formatWalletAddress(address).ellipsis(6,6)
        }
    }

    private fun checkFirstBitCoin() {
        val firstSwitchBitcoin = WalletHelper.isBitcoinNetwork() && SPUtils.getInstance()
            .getBoolean(SP_KEY_FIRST_SWITCH_TO_BITCOIN, true)
        if (firstSwitchBitcoin)
            NoteDialog().apply {
                icon = R.drawable.icon_bitcoin_beta
                title = Utils.getApp().getString(R.string.supports_bitcoin_beta)
                message = Utils.getApp().getString(R.string.supports_bitcoin_beta_desc)
                confirmText = Utils.getApp().getString(R.string.Okay)
            }.show(requireActivity().supportFragmentManager, NoteDialog::class.java.name)
        SPUtils.getInstance().put(SP_KEY_FIRST_SWITCH_TO_BITCOIN, false)
    }

}