package io.iotex.iopay.network.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ItemNetworkSwitchBinding
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class NetworkSwitchBinder(private val isDialog:Boolean = false) : ItemViewBinder<RPCNetwork, BaseBindVH<ItemNetworkSwitchBinding>>() {

    var itemClick: ((RPCNetwork) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemNetworkSwitchBinding> {
        val bind = ItemNetworkSwitchBinding.inflate(inflater,parent,false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemNetworkSwitchBinding>, item: RPCNetwork) {
        if (item.chainId == 0) {
            holder.bind.ivLogo.loadImage(R.drawable.icon_all_network)
        } else {
            holder.bind.ivLogo.loadSvgOrImage(item.logo, R.drawable.ic_network_default)
        }
        holder.bind.tvName.text = item.name
        if ((item.chainId == WalletHelper.getCurChainId() && !UserStore.getAllNetwork()) || (item.chainId == 0 && UserStore.getAllNetwork())) {
            if(isDialog){
                holder.bind.llItem.setBackgroundColor(ContextCompat.getColor(holder.bind.llItem.context, R.color.dialog_card_back))
            }else{
                holder.bind.llItem.setBackgroundColor(ContextCompat.getColor(holder.bind.llItem.context, R.color.color_card_back))
            }
            holder.bind.ivCheck.setVisible()
        } else {
            holder.bind.llItem.setBackgroundColor(ContextCompat.getColor(holder.bind.llItem.context, R.color.transparent))
            holder.bind.ivCheck.setGone()
        }
        holder.bind.llItem.setOnClickListener {
            WalletHelper.checkNetworkDialog(item){
                WalletHelper.switchNetwork(item)
                UserStore.setChainId(item.chainId)
                adapter.notifyDataSetChanged()
                itemClick?.invoke(item)
            }
        }
    }
}