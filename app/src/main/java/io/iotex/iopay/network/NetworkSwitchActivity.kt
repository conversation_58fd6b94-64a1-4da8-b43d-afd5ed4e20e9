package io.iotex.iopay.network

import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ActivityNetworkSwitchBinding
import io.iotex.iopay.network.item.NetworkSwitchBinder
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel
import io.iotex.iopay.support.eventbus.RefreshNetworkListEvent
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class NetworkSwitchActivity :
    BaseBindToolbarActivity<NetworkSwitchViewModel, ActivityNetworkSwitchBinding>(R.layout.activity_network_switch) {

    private val adapter = MultiTypeAdapter()

    override fun useEventBus(): Boolean {
        return true
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.switch_network))
        setToolbarSubmitThemeColor()
        setToolbarSubmitImage(R.drawable.icon_wallet_setting)
        setToolbarSubmitClick {
            startActivity<NetworkNodeSettingActivity>()
        }
        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        adapter.register(NetworkSwitchBinder().apply {
            itemClick = {
                finish()
            }
        })
        mBinding.recyclerView.adapter = adapter

        mBinding.mIvDelete.setOnClickListener {
            mBinding.mEtSearch.setText("")
        }

        mBinding.mEtSearch.addTextChangedListener {
            mViewModel.loadAllNetwork(it.toString())
        }
    }

    override fun initData() {
        mViewModel.loadAllNetwork()
        mViewModel.networkLiveData.observe(this) {
            val list = mutableListOf<RPCNetwork>()
            list.add(RPCNetwork("",0,getString(R.string.all_networks),"","","","","","","","",18,"","","","",1,"","","",0,"","","",true,"","","","","","","",""))
            list.addAll(it)
            adapter.items = list
            adapter.notifyDataSetChanged()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshNetworkListEvent(event: RefreshNetworkListEvent) {
        mViewModel.loadAllNetwork()
    }
}