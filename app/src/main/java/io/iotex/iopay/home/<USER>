package io.iotex.iopay.home

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.machinefi.walletconnect2.WalletConnectDAppEvent
import com.youth.banner.indicator.CircleIndicator
import io.iotex.api.HomeBannerQuery
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentHomeBinding
import io.iotex.iopay.home.item.HomeBannerAdapter
import io.iotex.iopay.home.widget.HomeTabView
import io.iotex.iopay.meta.SP_KEY_FIRST_SWITCH_TO_SOLANA
import io.iotex.iopay.network.NetworkSwitchActivity
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel
import io.iotex.iopay.setting.SwapMenuActivity
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.GiftPageEvent
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.MessageRedDotEvent
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshBalanceFinishEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.support.guide.initGuideView
import io.iotex.iopay.token.AddERC20Activity
import io.iotex.iopay.token.TokenFragment
import io.iotex.iopay.token.TokenListActivity
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.ui.WalletGuideStepDialog
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.NetworkUtils
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.NotificationUtils
import io.iotex.iopay.util.SPConstant.SP_GUIDE_STEP
import io.iotex.iopay.util.SPConstant.SP_HOME_POST_EVENT_TIME
import io.iotex.iopay.util.SPConstant.SP_NOTIFICATION_OPTION_BEFORE
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.NftSearchActivity
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.dialog.HomeEventDialog
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.dialog.UpdaterDialog
import io.iotex.iopay.wallet.home.WalletCardFragment
import io.iotex.iopay.wallet.home.HomeViewModel
import io.iotex.iopay.wallet.home.dialog.HomeNetworkErrorDialog
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import io.iotex.iopay.xapp.wc.WcManagerActivity
import io.iotex.iopay.xapp.wc.WcManagerViewModel
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

@SuppressLint("CommitTransaction")
class HomeFragment : BaseBindFragment<HomeViewModel, FragmentHomeBinding>(R.layout.fragment_home) {

    private var updaterDialog: UpdaterDialog? = null

    private var selectPosition = 0

    private val mNetworkSwitchViewModel by lazy {
        ViewModelProvider(this)[NetworkSwitchViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mWcManagerViewModel by lazy {
        ViewModelProvider(this)[WcManagerViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val walletCardFragment by lazy {
        WalletCardFragment()
    }

    private val nftListFragment by lazy {
        NftListFragment()
    }

    private val tokenFragment by lazy {
        TokenFragment()
    }

    private val fragments = mutableListOf<Fragment>()

    override fun useEventBus(): Boolean {
        return true
    }

    @SuppressLint("CommitTransaction")
    override fun initView() {
        mBinding.llChangeWallet.setOnClickListener {
            SwitchWalletActivity.startActivity(requireContext())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWITCH_WALLET)
            PageEventUtil.logEvent(PageEventUtil.MYWALLETS)
        }

        if (!NetworkUtils.isConnected(requireContext())) {
            HomeNetworkErrorDialog().show(
                childFragmentManager,
                System.currentTimeMillis().toString()
            )
        } else {
            NetworkUtils.checkNetwork(WalletHelper.getCurRpcUrl())
        }

        if(childFragmentManager.findFragmentByTag("walletCardFragment") == null){
            childFragmentManager.beginTransaction().add(R.id.flCard, walletCardFragment,"walletCardFragment").commitNow()
        }

        mBinding.refreshLayout.setOnRefreshListener {
            mViewModel.fetchHomeBanner()
            mNetworkSwitchViewModel.fetchNetworkList()
            mViewModel.curNetWork()
            EventBus.getDefault().post(MainPullRefresh())
        }

        mBinding.scrollView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            if (mBinding.tabLayout.top > scrollY) {
                mBinding.tabLayoutTop.setGone()
            } else {
                mBinding.tabLayoutTop.setVisible()
            }
        }

        mBinding.ivSetting.setOnClickListener {
            SwapMenuActivity.startActivity(requireActivity(), false)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_SETTING)
        }

        mBinding.rlGift.isVisible = UserStore.getSwitchGiftCenter()
        mBinding.ivGift.setOnClickListener {
            EventBus.getDefault().post(GiftPageEvent(false))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_GIFT_CENTER)
        }

        mBinding.mDragBtnWc.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WC_CLICK_FLOAT_BUTTON)
            WcManagerActivity.startActivity(requireContext())
        }
        mBinding.llNetwork.setOnClickListener {
            showNetworkSwitchDialog()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_SWITCH_NETWORK)
        }

        mBinding.tvAllNetwork.setOnClickListener {
            showNetworkSwitchDialog()
        }

        PageEventUtil.logEvent(PageEventUtil.MENUWALLET)

        if(UserStore.getAllNetwork()){
            showAllNetwork()
        }

        walletGuidPage(mBinding.root)

        val ivParams = mBinding.banner.layoutParams
        ivParams.width = ScreenUtils.getScreenWidth() - 30.dp2px()
        ivParams.height = (ivParams.width * 110 / 345)
        mBinding.banner.layoutParams = ivParams
    }

    private fun showNetworkSwitchDialog() {
        startActivity(Intent(context, NetworkSwitchActivity::class.java))
    }

    private fun showTokenList() {
        showTakenAdd(mBinding.tabLayout)
        showTakenAdd(mBinding.tabLayoutTop)
    }

    private fun showMemeList() {
        showMemeTab(mBinding.tabLayout)
        showMemeTab(mBinding.tabLayoutTop)
    }

    private fun showNftList() {
        showNftSearch(mBinding.tabLayout)
        showNftSearch(mBinding.tabLayoutTop)
        PageEventUtil.logEvent(PageEventUtil.NFTS)
    }

    private fun setTabLayoutClick(view: View) {
        view.findViewById<HomeTabView>(R.id.tabView)?.apply {
            onSelect = {
                selectPosition = it
                selectTab(mBinding.tabLayout, it)
                selectTab(mBinding.tabLayoutTop, it)
                if(it == 0){
                    showTokenList()
                } else if(it == fragments.size-1 && !WalletHelper.isBitcoinNetwork()){
                    showNftList()
                } else {
                    showMemeList()
                }
                if (it < fragments.size) {
                    childFragmentManager.beginTransaction().replace(R.id.flList, fragments[it])
                        .commitNow()
                }

                kotlin.runCatching {
                    mViewModel.tabLiveData.value?.get(it)
                }.getOrNull()?.let { name ->
                    val bundle = Bundle()
                    bundle.putString("tab", name)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_TAB, bundle)
                }
            }
        }
        view.findViewById<View>(R.id.ivTokenSearch).setOnClickListener {
            if(UserStore.getTokenEmpty(WalletHelper.getCurChainId())){
                startActivity(Intent(requireContext(), AddERC20Activity::class.java))
            }else{
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_CLICK_ADDTOKEN)
                startActivity(Intent(requireContext(), TokenListActivity::class.java))
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_TOKEN_SEARCH)
        }
        view.findViewById<View>(R.id.ivNftSearch).setOnClickListener {
            requireActivity().startActivity<NftSearchActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_SEARCH)
        }
        val ivHideCheck = view.findViewById<ImageView>(R.id.ivHideCheck)
        if(UserStore.getHideAssetsCheck()){
            ivHideCheck.setImageResource(R.drawable.icon_hide_assets_check)
        } else {
            ivHideCheck.setImageResource(R.drawable.icon_hide_assets_uncheck)
        }
        ivHideCheck.setOnClickListener {
            val check = UserStore.getHideAssetsCheck()
            UserStore.setHideAssetsCheck(!check)
            if(!check){
                ivHideCheck.setImageResource(R.drawable.icon_hide_assets_check)
            } else {
                ivHideCheck.setImageResource(R.drawable.icon_hide_assets_uncheck)
            }
            EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_HIDE_ASSETS)
        }

        view.findViewById<RelativeLayout>(R.id.mRlTabLayout).setOnClickListener {
            //no thing.  just cover touch event
        }
    }

    private fun showPosition(position: Int){
        selectTab(mBinding.tabLayout, position)
        selectTab(mBinding.tabLayoutTop, position)
        if(position == 0){
            showTokenList()
            childFragmentManager.beginTransaction().replace(R.id.flList, tokenFragment)
                .commitNow()
        }
    }

    override fun initData() {
        mViewModel.curNetWork()
        mViewModel.curWallet()
        mViewModel.fetchHomeBanner()
        initViewPage()
    }

    private fun initViewPage(){
        setTabLayoutClick(mBinding.tabLayout)
        setTabLayoutClick(mBinding.tabLayoutTop)
    }

    override fun initEvent() {
        mViewModel.networkLiveData.observe(this) {
            showPosition(selectPosition)
            if(UserStore.getAllNetwork())return@observe
            mBinding.tvAllNetwork.isVisible = UserStore.getAllNetwork()
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
        }
        mViewModel.tabLiveData.observe(this){
            if(!UserStore.getAllNetwork()){
                changeTab(mBinding.tabLayout, it)
                changeTab(mBinding.tabLayoutTop, it)
            }
            fragments.clear()
            fragments.add(tokenFragment)
            it.forEach { category ->
                if(childFragmentManager.findFragmentByTag(category) == null){
                    val memeFragment =MemeFragment.newInstance(category)
                    fragments.add(memeFragment)
                }
            }
            if(!WalletHelper.isBitcoinNetwork()){
                fragments.add(nftListFragment)
            }
        }
        mViewModel.walletLiveData.observe(this) {
            if (it.isAAWallet()) {
                mBinding.ivAA.setVisible()
                mBinding.ivWatch.setGone()
            } else if (it.isWatch) {
                mBinding.ivAA.setGone()
                mBinding.ivWatch.setVisible()
            } else {
                mBinding.ivAA.setGone()
                mBinding.ivWatch.setGone()
            }
            mBinding.tvWalletName.text = it.alias
            mBinding.icWalletImg.loadSvgOrImage(it.avatar, R.drawable.icon_wallet_default)
        }
        mViewModel.homeBannerLiveData.observe(viewLifecycleOwner) {
            handleBanners(it)
        }
        mViewModel.postLiveData.observe(viewLifecycleOwner) {
            if (!it?.post_img().isNullOrEmpty()) {
                if (SPUtils.getInstance().getString(SP_HOME_POST_EVENT_TIME)
                    == it?.id().toString() + DateTimeUtils.formatYMD(System.currentTimeMillis())
                ) return@observe
                Glide.with(this).asBitmap().load(it.post_img()).into(object :
                    CustomTarget<Bitmap>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap>?
                    ) {
                        if (isMainActivity()) {
                            HomeEventDialog(it.post_title(), resource).show(
                                childFragmentManager,
                                System.currentTimeMillis().toString()
                            )
                            SPUtils.getInstance().put(
                                SP_HOME_POST_EVENT_TIME,
                                it.id()
                                    .toString() + DateTimeUtils.formatYMD(System.currentTimeMillis())
                            )
                        }
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                        //nothing.
                    }

                })
            }
        }
        mViewModel.firstSolanaLiveData.observe(this){
            if(it) {
                NoteDialog().apply {
                    icon = R.drawable.icon_solana_logo
                    title = Utils.getApp().getString(R.string.iopay_now_supports_solana)
                    message =
                        Utils.getApp().getString(R.string.create_or_import_your_solana_wallet)
                    confirmText = Utils.getApp().getString(R.string.add_solana_wallet)
                    onConfirm = {
                        WalletAddActivity.startActivity(requireContext())
                    }
                }.show(requireActivity().supportFragmentManager, NoteDialog::class.java.name)
                SPUtils.getInstance().put(SP_KEY_FIRST_SWITCH_TO_SOLANA, false)
            }
        }
        mViewModel.fetchAppVersion()
        mViewModel.versionCodeLiveData.observe(this) {
            val update = AppUtils.getAppVersionCode() < it.target_version_code()
            UserStore.setUpdateApp(update)
            val ignore = UserStore.getIgnoreVersion()
            if (update && ignore != it.target_version_code()) {
                if (updaterDialog == null) {
                    updaterDialog = UpdaterDialog(it)
                    updaterDialog?.show(
                        childFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                }
            }
            showRed()
        }

        mWcManagerViewModel.queryConnectDApp()
        mWcManagerViewModel.connectLiveData.observe(this) {
            if (it.isEmpty()) {
                mBinding.mDragBtnWc.visibility = View.GONE
            } else {
                mBinding.mDragBtnWc.visibility = View.VISIBLE
            }
        }
    }

    fun isMainActivity(): Boolean {
        return ActivityUtils.getTopActivity() is MainActivity
    }

    private fun showNftSearch(view: View) {
        val llToken = view.findViewById<View>(R.id.llToken)
        val ivNftSearch = view.findViewById<View>(R.id.ivNftSearch)
        llToken.setGone()
        if (UserStore.getAllNetwork()) {
            ivNftSearch.setGone()
        } else {
            ivNftSearch.setVisible()
        }
    }

    private fun showTakenAdd(view: View) {
        val llToken = view.findViewById<View>(R.id.llToken)
        val ivNftSearch = view.findViewById<View>(R.id.ivNftSearch)
        ivNftSearch.setGone()
        if (UserStore.getAllNetwork()) {
            llToken.setGone()
        } else {
            llToken.setVisible()
        }
    }

    private fun showMemeTab(view: View) {
        val llToken = view.findViewById<View>(R.id.llToken)
        val ivNftSearch = view.findViewById<View>(R.id.ivNftSearch)
        ivNftSearch.setGone()
        llToken.setGone()
    }

    private fun changeTab(view: View, list: List<String> = arrayListOf()){
        val tabView = view.findViewById<HomeTabView>(R.id.tabView)
        if (UserStore.getAllNetwork()) {
            tabView.setItem(arrayListOf(getString(R.string.token)))
        } else if (WalletHelper.isBitcoinNetwork()) {
            tabView.setItem(arrayListOf(getString(R.string.brc_20)))
        } else {
            val tab = arrayListOf(getString(R.string.token))
            tab.addAll(list)
            tab.add(getString(R.string.nft_string))
            tabView.setItem(tab)
        }
        tabView.setSelect(selectPosition)
    }

    private fun selectTab(view: View, position:Int){
        val tabView = view.findViewById<HomeTabView>(R.id.tabView)
        tabView.setSelect(position)
    }

    private fun handleBanners(banners: List<HomeBannerQuery.Home_banner>) {
        mBinding.banner.setBackgroundResource(0)
        if (banners.isEmpty()) {
            mBinding.banner.visibility = View.GONE
        } else {
            mBinding.banner.visibility = View.VISIBLE
        }
        mBinding.banner.setTouchSlop(0)
        mBinding.banner.setLoopTime(4500)
        mBinding.banner.addBannerLifecycleObserver(viewLifecycleOwner)
            .setIndicator(CircleIndicator(requireActivity()))
            .setAdapter(HomeBannerAdapter(banners).apply {
                setOnBannerListener { data, _ ->
                    SchemeUtil.goto(requireContext(), data.link())
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_BANNER)
                }
            })
    }

    private fun notificationOption() {
        val notifyBefore = SPUtils.getInstance().getBoolean(SP_NOTIFICATION_OPTION_BEFORE)
        val deviceToken =
            SPUtils.getInstance().getString(IoPayConstant.DEVICE_TOKEN, "")
        if (!NotificationUtils.isOpenNotification() && !notifyBefore && deviceToken.isNotEmpty()) {
            activity?.let {
                if (!it.isFinishing && !it.isDestroyed) {
                    SPUtils.getInstance().put(SP_NOTIFICATION_OPTION_BEFORE, true)
                    NoticePopupWindow(
                        Utils.getApp().getString(R.string.open_notification_confirm_title),
                        Utils.getApp().getString(R.string.please_turn_on_app_notifications_to_stay),
                        null,
                        confirmAction = {
                            val intent = Intent()
                            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                            val uri = Uri.fromParts("package", it.packageName, null)
                            intent.data = uri
                            it.startActivity(intent)
                        }).show()
                }
            }
        }
    }

    private fun walletGuidPage(view: View) {
        mViewModel.fetchGuide()
        mViewModel.showGuideLiveData.observe(this){
            if (it) {
                WalletGuideStepDialog(cancelAction = {
                    mViewModel.fetchHomePost()
                    mViewModel.checkFirstSolana()
                }, confirmAction = {
                    view.postDelayed({
                        initGuideView(view, this@HomeFragment) {
                            mViewModel.fetchHomePost()
                            mViewModel.checkFirstSolana()
                            SPUtils.getInstance().put(SP_GUIDE_STEP, true)
                        }
                    }, 1000)
                }).show(childFragmentManager, System.currentTimeMillis().toString())
            } else {
                mViewModel.fetchHomePost()
                mViewModel.checkFirstSolana()
                notificationOption()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletConnectDAppEvent(event: WalletConnectDAppEvent) {
        if (event.connected) {
            mWcManagerViewModel.insetConnect(event.walletConnectDApp)
        } else {
            mWcManagerViewModel.deleteConnect(event.walletConnectDApp.topic)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: NetworkSwitchEvent) {
        selectPosition = 0
        mViewModel.curNetWork()
        mViewModel.curWallet()
        mViewModel.fetchHomeBanner()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        mViewModel.curWallet()
        mWcManagerViewModel.disConnectAll()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchAllNetworkEvent(event: SwitchAllNetworkEvent) {
        selectPosition = 0
        mViewModel.curNetWork()
        mBinding.tvAllNetwork.isVisible = UserStore.getAllNetwork()
        if(UserStore.getAllNetwork()){
            showAllNetwork()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        mBinding.refreshLayout.isRefreshing = false
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshBalanceFinishEvent(event: RefreshBalanceFinishEvent) {
        mBinding.refreshLayout.isRefreshing = false
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageRedDotEvent(event: MessageRedDotEvent) {
        showRed()
    }

    private fun showRed() {
        val message = UserStore.getMessageCount() != 0
        val update = UserStore.getUpdateApp()
        mBinding.viewMenuRed.isVisible = message || update
    }

    private fun showAllNetwork(){
        changeTab(mBinding.tabLayout)
        changeTab(mBinding.tabLayoutTop)
        mBinding.ivNetwork.loadImage(
            R.drawable.icon_all_network,
            R.drawable.ic_network_default
        )
        showTokenList()
    }
}
