<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_guide_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/initial_guide_vp"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/initial_guide_indicator"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="@dimen/common_padding_vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <TextView
            android:id="@+id/initial_guide_start_enjoy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginTop="52dp"
            android:layout_marginStart="32dp"
            android:gravity="center"
            android:layout_marginEnd="32dp"
            android:text="@string/next_step"
            android:background="@drawable/btn_shape_gradient_common"
            android:textSize="14sp"
            android:layout_marginBottom="@dimen/dp_80"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />
        <io.iotex.iopay.ui.widget.IoViewPagerIndicator
            android:id="@+id/initial_guide_indicator"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_24"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp_20"
            app:ioIndicatorHeight="@dimen/dp_6"
            app:ioIndicatorPadding="@dimen/dp_12"
            app:ioSelectedIndicatorDrawable="@drawable/bg_initial_guide_indicator_selected"
            app:ioUnselectedIndicatorDrawable="@drawable/bg_initial_guide_indicator_unselected"
            app:layout_constraintTop_toBottomOf="@+id/initial_guide_start_enjoy"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>