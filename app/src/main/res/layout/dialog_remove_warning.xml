<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:tint="@color/color_title"
            android:src="@drawable/ic_close_vd_white_24"
            android:padding="@dimen/dp_10"
            android:layout_margin="@dimen/dp_5"
            android:layout_gravity="end"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"/>
        <ImageView
            android:layout_marginTop="-15dp"
            android:src="@drawable/icon_tips_notice"
            android:layout_width="@dimen/dp_52"
            android:layout_height="@dimen/dp_52"/>

        <TextView
            android:text="@string/warning"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginTop="@dimen/dp_10"
            android:textColor="@color/color_title"
            android:textSize="17sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tvContent"
            tools:text="@string/delete_wallet_confirm"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp_20"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/mBtnCancel"
                android:layout_margin="@dimen/dp_24"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_44"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@drawable/btn_stroke_gradient_common"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/color_617AFF"
                android:textSize="@dimen/font_size_common" />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/mBtnConfirm"
                android:layout_margin="@dimen/dp_24"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_44"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@drawable/btn_shape_gradient_common"
                android:text="@string/confirm"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_common" />
        </LinearLayout>
    </LinearLayout>

</layout>