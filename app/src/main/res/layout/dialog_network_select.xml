<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android" >
    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:tint="@color/color_title"
            android:src="@drawable/icon_lock_screen_close"
            android:padding="10dp"
            android:layout_margin="5dp"
            android:layout_gravity="end"
            android:layout_width="44dp"
            android:layout_height="44dp"/>
        <ImageView
            android:src="@drawable/icon_tips_notice"
            android:layout_width="64dp"
            android:layout_height="64dp"/>

        <TextView
            android:text="@string/message_notice"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_title"
            android:textSize="17sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/tvAddress"
            android:layout_marginTop="25dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="25dp"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:text="@string/adding_non_default_node"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/tvGetIt"
            android:text="@string/i_got_it"
            android:textSize="14sp"
            android:background="@drawable/btn_shape_gradient_common"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginTop="30dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="30dp"
            android:layout_width="match_parent"
            android:layout_height="36dp"/>
    </LinearLayout>

</layout>