<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:gravity="center"
        android:background="@drawable/shape_back_dialog_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:showDividers="middle">
        <TextView
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/connect_wallet"
            android:textColor="@color/color_title"
            android:textSize="@dimen/font_size_large" />

        <FrameLayout
            android:layout_marginTop="@dimen/dp_24"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44">
            <com.makeramen.roundedimageview.RoundedImageView
                app:riv_corner_radius="@dimen/dp_20"
                android:id="@+id/ivAppLogo"
                android:layout_gravity="center"
                android:src="@drawable/ic_network_default"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40" />
            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/ivNetwork"
                android:layout_gravity="end"
                android:src="@drawable/ic_network_default"
                app:riv_corner_radius="@dimen/dp_9"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"/>
        </FrameLayout>

        <TextView
            android:id="@+id/tvAppUrl"
            android:text="@string/big_mimo"
            android:textSize="14sp"
            android:layout_marginTop="@dimen/dp_12"
            android:textColor="@color/color_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@drawable/shape_card_back"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:gravity="center"
                android:paddingTop="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="@string/method"
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:text="@string/get_account"
                    android:textSize="14sp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="@dimen/dp_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="@string/wallet_address_only"
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvAddress"
                    tools:text="ioddddd"
                    android:text="@string/get_account"
                    android:textSize="14sp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:layout_marginStart="@dimen/dp_10"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_40"
                android:layout_marginBottom="@dimen/common_padding_vertical">

                <TextView
                    android:id="@+id/mBtnCancel"
                    android:layout_width="@dimen/btn_width_common"
                    android:layout_height="@dimen/btn_height_common"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:background="@drawable/btn_stroke_gradient_common"
                    android:text="@string/cancel"
                    android:textColor="@color/colorPrimary" />

                <TextView
                    android:id="@+id/mBtnConfirm"
                    android:layout_width="@dimen/btn_width_common"
                    android:layout_height="@dimen/btn_height_common"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_weight="1"
                    android:background="@drawable/btn_shape_gradient_common"
                    android:text="@string/confirm"
                    android:gravity="center"
                    android:textColor="@android:color/white" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</layout>