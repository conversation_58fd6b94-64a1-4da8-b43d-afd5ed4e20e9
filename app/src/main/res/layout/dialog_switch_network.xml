<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_back_dialog_center"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_20"
    android:paddingTop="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_20"
    android:paddingBottom="@dimen/dp_20">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/dialog_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/switch_network"
        android:textColor="@color/color_title"
        android:textSize="@dimen/font_size_large" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/frameLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/mIvLogo"
                app:riv_corner_radius="@dimen/dp_25"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_50"
                android:src="@drawable/ic_dapp_placeholder" />

        </FrameLayout>

        <TextView
            android:id="@+id/mTvDappHost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="3dp"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/mTvDappChain"
            app:layout_constraintStart_toEndOf="@+id/frameLayout"
            app:layout_constraintTop_toTopOf="@+id/frameLayout"
            tools:text="iotube.org" />

        <TextView
            android:id="@+id/mTvDappChain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="3dp"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/frameLayout"
            app:layout_constraintStart_toStartOf="@+id/mTvDappHost"
            app:layout_constraintTop_toBottomOf="@+id/mTvDappHost"
            tools:text="IoTeX" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/switch_title"
        android:textColor="@color/color_title_sub"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/switch_tips"
        android:textColor="@color/color_title_sub"
        android:textSize="12sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginBottom="@dimen/dp_18"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/mTvFromChain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_stroke_gray_r4"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_6"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp"
            tools:text="IoTeX" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            android:src="@drawable/ic_arrow_right" />

        <TextView
            android:id="@+id/mTvToChain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_stroke_gray_r4"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_6"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp"
            tools:text="IoTeX" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/mCancel"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_weight="1"
            android:background="@drawable/btn_stroke_gradient_common"
            android:text="@string/cancel"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/mConfirm"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_weight="1"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/confirm"
            android:textSize="14sp"
            android:textAllCaps="false"
            android:textColor="@android:color/white" />
    </LinearLayout>
</LinearLayout>
