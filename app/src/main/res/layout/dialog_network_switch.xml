<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/content"
        android:layout_gravity="bottom"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_back_dialog_bottom"
        android:orientation="vertical"
        android:showDividers="middle">

        <RelativeLayout
            android:layout_marginTop="@dimen/dp_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvTitle"
                android:layout_centerInParent="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/switch_network"
                android:textStyle="bold"
                android:layout_gravity="center"
                android:textColor="@color/color_title"
                android:textSize="17sp" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivClose"
                android:tint="@color/color_title"
                android:layout_alignParentEnd="true"
                android:layout_marginBottom="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_18"
                android:src="@drawable/icon_close_dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            tools:itemCount="5"
            tools:listitem="@layout/item_network_switch"
            android:layout_height="wrap_content" />

    </LinearLayout>

</layout>