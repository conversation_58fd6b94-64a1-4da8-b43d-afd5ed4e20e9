<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_card_back"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_25"
        android:paddingBottom="@dimen/dp_48">

        <ImageView
            android:layout_width="@dimen/dp_120"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:adjustViewBounds="true"
            android:src="@drawable/icon_aa_expired" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_28"
            android:text="@string/aa_wallet_expired"
            android:textColor="@color/color_title"
            android:textSize="17sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_50"
            android:gravity="center_horizontal"
            android:text="@string/wallet_expired_alert_1"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/wallet_expired_alert_2"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSwitchWallet"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36"
            android:layout_marginTop="@dimen/dp_28"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_50"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/switch_wallet"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvSendRecoveryEmail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/send_recovery_email_undeline"
            android:textColor="@color/color_617AFF"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_40">

            <ImageView
                android:layout_width="@dimen/dp_24"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_warning" />

            <TextView
                android:id="@+id/tvRecoveryNote"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/send_recovery_email_alert"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvContractUs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/contract_us_underline"
            android:textColor="@color/color_617AFF"
            android:textSize="14sp" />

    </LinearLayout>
</layout>
