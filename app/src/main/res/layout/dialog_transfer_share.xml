<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/llPoster"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="vertical"
                android:paddingBottom="@dimen/dp_20"
                android:background="@color/color_back"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_close"
                    android:padding="@dimen/dp_10"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"/>
                <LinearLayout
                    android:id="@+id/llTrans"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:src="@drawable/icon_app_logo_dark"
                        android:layout_marginTop="@dimen/dp_24"
                        android:layout_width="@dimen/dp_50"
                        android:layout_height="@dimen/dp_50"/>

                    <TextView
                        android:id="@+id/tvTransBlock"
                        android:layout_marginTop="@dimen/dp_10"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/color_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvTransMoney"
                        android:layout_marginTop="@dimen/dp_4"
                        android:textSize="14sp"
                        android:textColor="@color/color_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <TextView
                        android:id="@+id/tvTransStatus"
                        android:layout_marginTop="@dimen/dp_10"
                        android:textSize="14sp"
                        android:textColor="@color/color_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llNft"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="@dimen/dp_20"
                    android:layout_marginEnd="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.makeramen.roundedimageview.RoundedImageView
                            android:id="@+id/ivNft"
                            android:background="@drawable/shape_card_back"
                            app:riv_corner_radius="@dimen/dp_6"
                            android:layout_width="86dp"
                            android:layout_height="86dp"/>

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_marginStart="@dimen/dp_10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvNftName"
                                android:textStyle="bold"
                                android:textSize="16sp"
                                android:maxLines="1"
                                android:ellipsize="end"
                                android:textColor="@color/color_title_sub"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:id="@+id/tvNftId"
                                android:textSize="14sp"
                                android:layout_marginTop="@dimen/dp_5"
                                android:textColor="@color/color_title_sub"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvNftStatus"
                                android:textSize="14sp"
                                android:layout_marginTop="@dimen/dp_15"
                                android:textColor="@color/color_617aff"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="@dimen/dp_20"
                    android:orientation="vertical"
                    android:background="@drawable/shape_card_back"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15"
                    android:paddingTop="@dimen/dp_20"
                    android:paddingBottom="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:text="@string/time"
                            android:textSize="14sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvTime"
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_70"
                            android:layout_alignParentEnd="true"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:layout_marginTop="@dimen/dp_30"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:text="@string/from"
                            android:textSize="14sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvFrom"
                            android:textSize="14sp"
                            android:gravity="end"
                            android:layout_marginStart="@dimen/dp_70"
                            android:layout_alignParentEnd="true"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:layout_marginTop="@dimen/dp_30"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:text="@string/to"
                            android:textSize="14sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvTo"
                            android:textSize="14sp"
                            android:gravity="end"
                            android:layout_marginStart="@dimen/dp_70"
                            android:layout_alignParentEnd="true"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:layout_marginTop="@dimen/dp_30"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:text="@string/txid"
                            android:textSize="14sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvTxId"
                            android:textSize="14sp"
                            android:gravity="end"
                            android:layout_marginStart="@dimen/dp_70"
                            android:layout_alignParentEnd="true"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:background="@color/color_card_back"
                android:padding="@dimen/dp_20">

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="@dimen/dp_26"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:src="@drawable/icon_app_logo_dark" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:adjustViewBounds="true"
                            android:tint="@color/color_title"
                            android:src="@drawable/ic_iopay" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/share_nft_caption"
                        android:textColor="@color/color_title_sub"
                        android:textSize="11sp" />
                </LinearLayout>

                <com.makeramen.roundedimageview.RoundedImageView
                    app:riv_corner_radius="@dimen/dp_3"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="60dp"
                    android:src="@drawable/icon_share_qrcode" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30"
            android:gravity="center">

            <ImageView
                android:id="@+id/mIvDownload"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_50"
                android:background="@drawable/icon_share_download" />

            <ImageView
                android:id="@+id/mIvShare"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_50"
                android:layout_marginStart="@dimen/dp_40"
                android:background="@drawable/icon_share_to" />

        </LinearLayout>
    </LinearLayout>

</layout>