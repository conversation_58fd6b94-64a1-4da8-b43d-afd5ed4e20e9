<?xml version="1.0" encoding="utf-8"?>
<com.daimajia.swipe.SwipeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mSlItem"
    android:layout_width="match_parent"
    android:layout_marginTop="10dp"
    android:layout_height="48dp">

    <LinearLayout
        android:id="@+id/mLlDelete"
        android:layout_width="@dimen/dp_54"
        android:layout_height="match_parent"
        android:background="@color/color_E53737"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/icon_delete_white" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mLlRpcNode"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15">

        <View
            android:id="@+id/mRpcStatus"
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_6"
            android:background="@drawable/shape_circle_617aff" />

        <TextView
            android:id="@+id/mTvRpcUrl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_13"
            android:layout_marginEnd="@dimen/dp_13"
            android:textColor="@color/color_title"
            android:textSize="@dimen/font_size_common"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="http://babel-api.mainnet.iotex.io" />

        <ImageView
            android:id="@+id/ivEdit"
            android:src="@drawable/ic_edit"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"/>

    </LinearLayout>
</com.daimajia.swipe.SwipeLayout>

