<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.Toolbar
            android:id="@+id/react_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/gray_B4B8CB"
                android:textSize="17sp" />
        <TextView
                android:id="@+id/toolbar_submit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:gravity="center"
                android:paddingRight="@dimen/barcode_field_value_padding"
                android:paddingLeft="@dimen/barcode_field_value_padding"
                android:text="@string/next_step"
                android:visibility="gone"
                android:autoSizeTextType="uniform"
                android:autoSizeMaxTextSize="@dimen/font_size_common"
                android:autoSizeMinTextSize="@dimen/font_size_very_little"
                android:lines="1"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/font_size_common"/>

        <RelativeLayout
                android:id="@+id/toolbar_right"
                android:layout_width="?attr/actionBarSize"
                android:layout_height="?attr/actionBarSize"
                android:layout_gravity="end"
                android:layout_marginEnd="@dimen/dp_16"
                android:clickable="true"
                android:focusable="true">

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>
</layout>