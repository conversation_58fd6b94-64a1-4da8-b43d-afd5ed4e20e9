<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tooles="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/llContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_card_back"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llGmailContainer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_20">

            <TextView
                android:id="@+id/tvGmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_title"
                android:textSize="16sp"
                tooles:text="12345678" />

            <TextView
                android:id="@+id/tvGmailSuffix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="\@gmail.com"
                android:textColor="@color/color_title"
                android:textSize="16sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llQQContainer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_20">

            <TextView
                android:id="@+id/tvQQ"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_title"
                android:textSize="16sp"
                tooles:text="12345678" />

            <TextView
                android:id="@+id/tvQQSuffix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="\@qq.com"
                android:textColor="@color/color_title"
                android:textSize="16sp" />

        </LinearLayout>


    </LinearLayout>
</layout>
